from datetime import datetime
from typing import Optional

from pydantic import Field

from apps.connectors.integrations import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


class NozomiVantageV1EventSyncBookmark(TemplateVersionActionBookmark):
    """Bookmark model for Nozomi Vantage event synchronization."""

    last_alert_time: Optional[datetime] = Field(
        default=None, description="Timestamp of the last processed alert"
    )
    last_alert_id: Optional[str] = Field(
        default=None, description="ID of the last processed alert for deduplication"
    )


NozomiVantageV1Bookmarks = create_bookmarks_model(
    "NozomiVantageV1Bookmarks",
    {
        "event_sync": NozomiVantageV1EventSyncBookmark,
    },
)
