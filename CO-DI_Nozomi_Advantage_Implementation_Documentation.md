# DI - Nozomi Advantage

## Nozomi Advantage OCSF Integration Implementation Documentation

### Executive Summary

This document provides comprehensive implementation specifications for integrating Nozomi Advantage with our OCSF schema. Based on validated API documentation and response examples, we've determined that the DetectionFinding class is the optimal OCSF mapping target for Nozomi Advantage alerts.

The integration covers OT/ICS security alerts, including anomalies, operational issues, and security threats across industrial networks and critical infrastructure. The API provides detailed alert metadata including severity ratings, risk scores, asset information, and network context.

Key implementation challenges include normalizing Nozomi's 0-10 severity scale to OCSF's 0-6 scale, mapping alert status fields correctly, and handling source/destination endpoint information. This document provides detailed field mappings, transformation logic, and implementation guidance following our existing codebase patterns.

## API Overview

Based on the validated Nozomi Advantage API documentation, we need to use the following endpoints:

**List Alerts API: GET /api/v1/alerts**
- Used to retrieve a list of alerts with filtering options
- Returns an array of alert objects with summary information

**Specific Alert API: GET /api/v1/alerts/{id}**
- Used to retrieve detailed information about a specific alert
- The alert ID is obtained from the List Alerts API response

**Alert Comments API: GET /api/v1/alerts/{id}/comments**
- Used to retrieve comments associated with a specific alert
- Provides context about analyst activities

**Add Alert Comment API: POST /api/v1/alerts/{id}/comments**
- Used to add comments to an existing alert
- Returns confirmation of comment creation

**Acknowledge Alert API: POST /api/open/alerts/ack**
- Used to acknowledge or unacknowledge alerts
- Accepts an array of alert IDs and acknowledgment status

### References
- Nozomi Advantage API Documentation (available through partner/customer portal) Vantage Swagger

### Authentication

Authentication is done via Bearer Token:

© 2025 Critical Start All rights reserved

**Headers required:**
```
Authorization: Bearer <API Access Token>
```

To obtain the access token:
1. Use credentials (api key name and api key token) to authenticate via a sign-in endpoint /api/v1/keys/sign_in
2. Extract the bearer token from the response
3. Include the token in the Authorization header for subsequent requests

## Schema Recap

Nozomi Advantage provides security alerts through its API. The validated API response schema has been documented from direct API testing and is complete for mapping purposes.

### Event Sync Strategy

1. Use the List Alerts API to retrieve alerts within a specified time range
2. For each alert, normalize the data to the OCSF DetectionFinding class
3. Optionally retrieve comments for alerts that need additional context (OR provide comments via TAP?)

## Validated API Response Schema

The primary schema is the alert object returned by the `/api/v1/alerts` and `/api/v1/alerts/{id}` endpoints. Here is a complete example of the alert response:

```json
{
  "data": {
    "id": "a55c2b60-cba5-4d19-b1e7-be5809f5ce8c",
    "type": "alerts",
    "attributes": {
      "id": "a55c2b60-cba5-4d19-b1e7-be5809f5ce8c",
      "ack": false,
      "_can": {
        "trace_requests": true,
        "close_learn_alerts": false,
        "security_control_panel.tuning": false
      },
      "name": "Eng operations",
      "note": null,
      "time": 1743554405292,
      "id_dst": "**************",
      "id_src": "*************",
      "ip_dst": "**************",
      "ip_src": "*************",
      "status": "open",
      "counter": 1,
      "mac_dst": "f4:54:33:9f:22:3d",
      "mac_src": "00:0c:29:01:98:be",
      "parents": [],
      "uid_dst": null,
      "uid_src": null,
      "port_dst": null,
      "port_src": null,
      "protocol": "ethernetip",
      "severity": 10,
      "zone_dst": "Production Network B",
      "zone_src": "Production Network B",
      "dst_roles": "producer",
      "label_dst": null,
      "label_src": null,
      "src_roles": "consumer, engineering_station",
      "ti_source": "",
      "type_name": "Eng operations",
      "types_dst": [
        "OT_device"
      ],
      "types_src": [
        "-"
      ],
      "alert_info": {},
      "bpf_filter": "",
      "levels_dst": [
        "2"
      ],
      "levels_src": [
        "2"
      ],
      "properties": {
        "victims": [],
        "raised_by": "n2os_alert",
        "is_outdated": true,
        "n2os_version": "25.0.0-03042016_D5AB7",
        "is_dst_public": false,
        "is_src_public": false,
        "mitre_attack_for_ics": {
          "source": {
            "levels": [
              "2"
            ]
          },
          "software": [],
          "techniques": [
            {
              "id": "T0845",
              "name": "Program Upload",
              "count": 2,
              "tactic": "Collection"
            },
            {
              "id": "T0843",
              "name": "Program Download",
              "count": 2,
              "tactic": "Persistence"
            },
            {
              "id": "T0843",
              "name": "Program Download",
              "count": 2,
              "tactic": "Inhibit Response Function"
            },
            {
              "id": "T0843",
              "name": "Program Download",
              "count": 2,
              "tactic": "Impair Process Control"
            }
          ],
          "destination": {
            "types": [
              "Field Controller/RTU/PLC/IED"
            ],
            "levels": [
              "2"
            ]
          }
        },
        "mitre_attack_enterprise": {
          "techniques": []
        },
        "incident_key_confidence:EngOperations_**************-*************ethernetip": 1
      },
      "closed_time": 0,
      "description": "Eng operations made on device ************** issued by host *************",
      "is_incident": true,
      "is_security": true,
      "threat_name": "",
      "created_time": 1742472907801,
      "trigger_type": null,
      "incident_keys": [
        "EngOperations_**************-*************-ethernetip"
      ],
      "appliance_host": "Sandbox-P-Guardian1",
      "capture_device": "",
      "physical_links": null,
      "grouped_visible": true,
      "assertion_source": "",
      "custom_fields_dst": {},
      "custom_fields_src": {},
      "playbook_contents": null,
      "transport_protocol": "unknown",
      "sec_profile_visible": true,
      "mitre_attack_tactics": null,
      "additional_description": {},
      "mitre_attack_techniques": null,
      "edge_id": "a55c2b60-cba5-4d19-b1e7-be5809f5ce8c",
      "risk": 6,
      "trace_status": null,
      "trace_sha1": null,
      "record_created_at": 1743554820099,
      "revert_taken_action_status": null,
      "type_id": "INCIDENT:ENG-OPERATIONS",
      "trigger_id": null
    },
    "relationships": {
      "alerts": {
        "links": {
          "related": "/api/v1/alerts/a55c2b60-cba5-4d19-b1e7-be5809f5ce8c/alerts"
        },
        "meta": {
          "count": 2
        }
      },
      "timeline_events": {
        "links": {
          "related": "/api/v1/alerts/a55c2b60-cba5-4d19-b1e7-be5809f5ce8c/timeline_events"
        }
      },
      "comments": {
        "links": {
          "related": "/api/v1/alerts/a55c2b60-cba5-4d19-b1e7-be5809f5ce8c/comments"
        },
        "meta": {
          "count": 0
        }
      },
      "alert_close_options": {
        "links": {
          "related": "/api/v1/admin/alert_close_options"
        }
      },
      "assignees": {
        "data": []
      },
      "site": {
        "links": {
          "related": "/api/v1/sites/1458ac91-27f0-467d-b66d-b08a1719c040"
        },
        "data": {
          "type": "sites",
          "id": "1458ac91-27f0-467d-b66d-b08a1719c040"
        }
      },
      "from_asset": {
        "links": {
          "related": "/api/v1/assets/2da04fc5-3032-4ec6-b588-69dd39b73adc"
        },
        "data": {
          "type": "assets",
          "id": "2da04fc5-3032-4ec6-b588-69dd39b73adc"
        }
      },
      "to_asset": {
        "links": {
          "related": "/api/v1/assets/05d66007-8251-464e-bf13-06102ec8d2eb"
        },
        "data": {
          "type": "assets",
          "id": "05d66007-8251-464e-bf13-06102ec8d2eb"
        }
      },
      "wireless_network": {
        "links": {}
      },
      "sensor": {
        "links": {
          "related": "/api/v1/sensors/9b497968-2cea-4607-9355-5808a7fcdda8"
        },
        "data": {
          "type": "sensors",
          "id": "9b497968-2cea-4607-9355-5808a7fcdda8"
        }
      }
    },
    "links": {
      "self": "/api/v1/alerts/a55c2b60-cba5-4d19-b1e7-be5809f5ce8c",
      "collection": "/api/v1/alerts"
    }
  },
  "links": {
    "self": "/api/v1/alerts/a55c2b60-cba5-4d19-b1e7-be5809f5ce8c"
  },
  "meta": {
    "sdr": "r"
  },
  "included": []
}
```

## Alert Types in Nozomi Advantage

Based on validated API responses, we've observed alert types including:
- "Eng operations" (Engineering Operations)
- "INCIDENT:ENG-OPERATIONS" (type_id format)

**[VALIDATION REQUIRED]** A complete list of alert types should be validated during implementation.

## Alert Status in Nozomi Advantage

Based on validated API responses, we've observed the following alert statuses:
- "open" - Default state for new alerts
- "closed" - Alerts that have been resolved

**[VALIDATION REQUIRED]** Additional status values like "investigating" may exist but require validation during implementation.

## OCSF Mapping

### DetectionFinding Class Mapping

| Nozomi Advantage Field | OCSF Field | Notes | Ignored Reason |
|------------------------|------------|-------|----------------|
| id | metadata.uid | Unique identifier for the alert | |
| attributes.name | finding_info.title | Alert name/title | |
| attributes.description | message | Human-readable description of the alert | |
| attributes.description | finding_info.desc | | |
| attributes.time | time_dt | Convert epoch milliseconds | |
| attributes.created_time | created_time_dt, finding_info.created_time_dt | Convert epoch milliseconds | |
| attributes.closed_time | end_time_dt | Convert epoch milliseconds if > 0 | |
| attributes.severity | severity_id | Map Nozomi 0-10 scale to OCSF 0-6 scale. See Severity Mapping table below | |
| attributes.risk | risk_score | Direct copy of risk score | |
| attributes.status | status_id | Map to DetectionStatus enum | |
| attributes.type_id | | Direct copy of readable type name | Duplicate of type_name |
| attributes.type_name | finding_info.types | | |
| attributes.counter | finding_info.related_events_count | Direct copy of occurrence count | |
| attributes.is_incident | severity_id | Override to 5 (Critical) if true | |
| attributes.properties.raised_by | metadata.product.feature | Source of the alert (e.g., "n2os_alert") | |
| attributes.properties.n2os_version | metadata.product.version | Product version information | |
| attributes.appliance_host | device.hostname | Specific product/appliance name | |
| attributes.protocol | enrichment.value | Application protocol (e.g., "ethernetip") Consider using an ocsf.Enrichment and set the value to attributes.protocol. Additionally, we could add `data={"protocol": attributes.protocol}`. I'm not sure the best place to capture this application protocol information. | |
| attributes.transport_protocol | evidence.connection_info.protocol_name | Transport layer protocol (e.g., "unknown") | |
| attributes.properties.mitre_attack_for_ics.techniques | finding_info.attacks | Convert to MitreAttack objects | |
| attributes.properties.mitre_attack_enterprise.techniques | finding_info.attacks | Convert to MitreAttack objects | |
| attributes.properties.incident_keys | | | For internal Nozomi use only, not relevant to security event |
| | metadata.vendor_name | Static: "Nozomi Networks" | |
| attributes._can | | | Permission object, not relevant to security event |
| attributes.note | | | Usually null; comments API used instead |
| attributes.parents | | | Alert relationship info not directly mappable to OCSF |
| attributes.alert_info | | | Usually empty object based on sample response; specific details in other fields |
| attributes.bpf_filter | enrichment.data | Create an enrichment with name="bpf_filter", type="packet_filter". Contains Berkeley Packet Filter syntax (e.g., "ether host 00:0c:29:28:dd:c5") used for packet capture related to the alert | |
| attributes.threat_name | | | Usually empty; description and type_name provide context |
| attributes.trigger_type, attributes.trigger_id | | | Alert trigger mechanism details, not relevant to security finding |
| attributes.sec_profile_visible, attributes.grouped_visible | | | UI display flags, not relevant to security event |
| attributes.record_created_at | | | Redundant with created_time |
| attributes.ack | status | Map to DetectionStatus using following logic: if 'true', set to IN_PROGRESS unless alert's status is already set to Closed/Resolved. | |

### Source and Destination Endpoint Mapping as an EvidenceArtifact

| Nozomi Advantage Field | OCSF Field | Notes | Ignored Reason |
|------------------------|------------|-------|----------------|
| attributes.id_src | evidences[].src_endpoint.uid | Source asset identifier | |
| attributes.ip_src | evidences[].src_endpoint.ip | Source IP address | |
| attributes.mac_src | evidences[].src_endpoint.mac | Source MAC address | |
| attributes.port_src | evidences[].src_endpoint.port | Source port number (can be null) | |
| attributes.zone_src | evidences[].src_endpoint.zone | Source network zone | |
| attributes.types_src | evidences[].src_endpoint.type | Join array with comma; special handling for "-" value | |
| attributes.src_roles | enrichments[].data | Source asset role Create an enrichment with name="src_roles", type="asset_roles" to store the source asset roles | |
| attributes.label_src | | | No direct mapping for this field. |
| attributes.custom_fields_src | | | No direct mapping for this field |
| attributes.properties.mitre_attack_for_ics.source | | | No direct OCSF mapping |
| attributes.properties.is_src_public | | Boolean indicating if source is public | No direct OCSF mapping |
| attributes.levels_src | vendor_data.nozomi.levels_src | Purdue levels for OT asset | No direct OCSF mapping for Purdue levels |
| attributes.uid_src | | | Usually null; duplicative with id_src |
| attributes.id_dst | evidences[].dst_endpoint.uid | Destination asset identifier | |
| attributes.ip_dst | evidences[].dst_endpoint.ip | Destination IP address | |
| attributes.mac_dst | evidences[].dst_endpoint.mac | Destination MAC address | |
| attributes.port_dst | evidences[].dst_endpoint.port | Destination port number (can be null) | |
| attributes.zone_dst | evidences[].dst_endpoint.zone | Destination network zone | |
| attributes.types_dst | evidences[].dst_endpoint.type | Join array with comma | |
| attributes.dst_roles | enrichments[].data | Destination asset role Create an enrichment with name="dst_roles", type="asset_roles" to store the source asset roles | |
| attributes.label_dst | | | No direct OCSF mapping |
| attributes.custom_fields_dst | | | No direct OCSF mapping |
| attributes.properties.mitre_attack_for_ics.destination | | | No direct OCSF mapping |
| attributes.properties.is_dst_public | | Boolean indicating if destination is public | No direct OCSF mapping |
| attributes.levels_dst | | Purdue levels for OT asset | No direct OCSF mapping for Purdue levels |
| attributes.uid_dst | | | Usually null; duplicative with id_dst |
| attributes.properties.victims | | | Usually empty array; victim info captured in dst_endpoint |

### MITRE ATT&CK Mapping

| Nozomi Advantage Field | OCSF Field | Notes | Ignored Reason |
|------------------------|------------|-------|----------------|
| properties.mitre_attack_for_ics.techniques | finding_info.attacks | Array of MITRE ATT&CK technique objects | |
| properties.mitre_attack_enterprise.techniques | finding_info.attacks | Array of MITRE ATT&CK technique objects | |

### Additional Fields Not Mapped to OCSF

| Nozomi Field | Notes | Ignored Reason |
|--------------|-------|----------------|
| record_created_at | Record creation timestamp | Redundant with created_time |
| is_security | Security flag | Used for internal classification logic |
| edge_id | Graph edge ID | Internal identifier, redundant with id |
| physical_links | Physical connection details | Usually null; not in core schema |
| trace_status, trace_sha1 | Packet capture references | Trace/PCAP details not in core schema |
| revert_taken_action_status | Action revert status | Remediation history details not in core schema |
| assertion_source | Source of assertion | Internal field not relevant to security event |
| playbook_contents | Automated response details | Usually null; not relevant to detection event |
| mitre_attack_tactics, mitre_attack_techniques | Additional MITRE fields | Usually null; techniques captured in properties |
| additional_description | Extra description | Usually empty; main description field is sufficient |
| properties.incident_key_confidence:* | Confidence scores | Internal scoring system not relevant to OCSF schema |

### Severity Mapping Table

| Nozomi Severity (1-10) | OCSF Severity (0-6) | Notes |
|------------------------|---------------------|-------|
| 0-1 | 0 | Informational |
| 2-3 | 1 | Low |
| 4-5 | 2 | Medium |
| 6-7 | 3 | High |
| 8-10 | 4 | Critical |
| Any if is_incident=true | 4 | Critical (override) |

### Status Mapping Table

| Nozomi Status | OCSF Status ID | OCSF Status Name | Notes |
|---------------|----------------|------------------|-------|
| "open" | 1 | NEW | Confirmed in example response |
| "closed" | 7 | CLOSED | Not in example but confirmed in Phase 3 |
| **[VALIDATION REQUIRED]** "investigating" | 2 | INVESTIGATING | Needs validation |
| **[VALIDATION REQUIRED]** "resolved" | 8 | RESOLVED | Needs validation |

## Response Actions

| Portal Action (TAP) | Type | Operates on Target (Entity Class) | Identifier (Entity targeted by this action) | API Call | Data Connector Action | Notes |
|---------------------|------|-----------------------------------|---------------------------------------------|----------|----------------------|-------|
| acknowledge_alert | Response | update_lifecycle_status | VendorLifecycleIdentifier | Event | Acknowledges an alert | POST /api/open/alerts/ack with {"ids": ["alertId"],"value": true} |
| unacknowledge_alert | Response | update_lifecycle_status | VendorLifecycleIdentifier | Event | Unacknowledges an alert | POST /api/open/alerts/ack with {"ids": ["alertId"],"value": false} |
| on_comment | Response | add_alert_comment | VendorLifecycleIdentifier | Event | Adds a comment to an alert | POST /api/v1/alerts/{id}/comments with {"text":"comment text"} |

## Implementation Notes

1. The Nozomi API may require pagination handling when dealing with large result sets
2. Alert status and comment history should be monitored for updates to existing alerts
3. Time fields need consistent conversion from epoch milliseconds to both Unix timestamp and ISO format

## Field Mapping Challenges

Several Nozomi Advantage fields require special handling for OCSF mapping:

1. **Severity Normalization:**
   - Nozomi uses a 0-10 scale that must be normalized to OCSF's 0-6 scale
   - The is_incident flag should override severity to Critical (5) when true

2. **Asset Type Arrays:**
   - The types_src and types_dst fields are arrays that need to be joined into strings

## Required API Authentication Information

To connect to the Nozomi Advantage API, we need:
1. API Username and Password (for obtaining the Bearer token)
2. API Base URL

## Required API Configuration Parameters

1. **API Base URL (required):** API endpoint URL
2. **API Key Name (required):** Name of API Key for authentication
3. **API Key (required):** API Key for authentication
4. **Event Query Interval (optional):** Time interval between API polls (default: 5 minutes)
5. **Max Alerts Per Query (optional):** Maximum number of alerts to retrieve per query (affects pagination)
6. **Alert Types Filter (optional):** List of alert types to include/exclude
7. **Minimum Severity (optional):** Minimum severity level to include (1-10 scale)

## Mapping Validation with Example Response

A real-world example of an Alert API response was analyzed, which validated most of our proposed mappings with some observations:

### Observations from Sample Data

1. **Time Format:**
   - Time values are provided as epoch milliseconds (13 digits)
   - Closed alerts have a non-zero closed_time value

2. **Status Values:**
   - Observed status values include "open" and "closed"
   - Additional status values may require validation

3. **Asset Information:**
   - Source and destination fields may be null when not applicable
   - The types_src and types_dst arrays contain asset type classifications

## Django Implementation Plan

The implementation for Nozomi Advantage should follow the pattern of existing vendor integrations in the codebase. The primary components include:

1. **API Client:** Handles authentication, request generation, and response parsing
2. **Data Transformation:** Maps Nozomi fields to OCSF schema
3. **Sync Service:** Manages periodic data sync and deduplication
4. **Response Actions:** Implements acknowledge/unacknowledge and comment actions

## Validation Requirements

The following aspects require validation during implementation:

1. **[VALIDATION REQUIRED]** Confirm all possible alert status values
2. **[VALIDATION REQUIRED]** Validate severity mapping scale
3. **[VALIDATION REQUIRED]** Confirm timestamp format consistency
4. **[VALIDATION REQUIRED]** Validate all possible values for types_src and types_dst

---

## Metadata

- **Document Version:** 1.0.0
- **Last Updated:** July 3, 2025
- **Validation Status:** Partially validated with example API responses
- **Completion Status:** Phase 4 - Implementation Documentation
- **Dependencies:** DetectionFinding OCSF class, Nozomi Advantage API credentials

## Conclusion

This implementation document provides a comprehensive mapping for integrating Nozomi Advantage alerts into our OCSF schema using the DetectionFinding class. The mapping ensures clarity between source and target fields, with proper separation of unmapped Nozomi fields.

Our approach offers detailed mapping for alert data including endpoint information, severity normalization, and comment handling. While the mapping is complete based on available documentation and example responses, several items require validation with live API testing. The implementation plan provides a clear roadmap for developing the connector following our established patterns.

Upon implementation and validation, this connector will enable comprehensive visibility into OT/ICS security alerts from Nozomi Advantage, enhancing the security monitoring capabilities for industrial networks and critical infrastructure.

---

© 2025 Critical Start All rights reserved